# Discovered Components

This is an auto-generated list of components discovered by [nuxt/components](https://github.com/nuxt/components).

You can directly use them in pages and other components without the need to import them.

**Tip:** If a component is conditionally rendered with `v-if` and is big, it is better to use `Lazy` or `lazy-` prefix to lazy load.

- `<CommonChartComponent>` | `<common-chart-component>` (components/common/ChartComponent.vue)
- `<CommonDivTextSearch>` | `<common-div-text-search>` (components/common/DivTextSearch.vue)
- `<CommonDocumentAIChat>` | `<common-document-a-i-chat>` (components/common/DocumentAIChat.vue)
- `<CommonFileUploadModal>` | `<common-file-upload-modal>` (components/common/FileUploadModal.vue)
- `<DocumentCompare>` | `<document-compare>` (components/document/DocumentCompare.vue)
- `<DocumentViewer>` | `<document-viewer>` (components/document/DocumentViewer.vue)
- `<IndexDashboardOverview>` | `<index-dashboard-overview>` (components/index/DashboardOverview.vue)
- `<IndexLatestUpdates>` | `<index-latest-updates>` (components/index/LatestUpdates.vue)
- `<IndexReviewTable>` | `<index-review-table>` (components/index/ReviewTable.vue)
- `<IndexSourceDistribution>` | `<index-source-distribution>` (components/index/SourceDistribution.vue)
