<template>
  <div class="updates-page-wrapper">
    <div class="lawyer-page-container">
      <!-- 主卡片容器 -->
      <div class="lawyer-card">
        <!-- 页面头部 -->
        <div class="lawyer-updates-header">
          <h2 class="lawyer-title">智库更新与通知</h2>
          <div class="lawyer-filter-tabs">
            <button
              v-for="filter in filterOptions"
              :key="filter.value"
              :class="[
                'lawyer-filter-btn',
                { 'lawyer-filter-btn-active': activeFilter === filter.value },
              ]"
              @click="() => setActiveFilter(filter.value)"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>

        <!-- 加载中状态 -->
        <div v-if="loading" class="lawyer-loading-container">
          <a-spin size="large" tip="正在加载数据..." />
        </div>

        <!-- 法规更新列表 -->
        <div v-else class="lawyer-updates-list">
          <div
            v-for="item in paginatedUpdates"
            :key="item.id"
            class="lawyer-update-item"
          >
            <div :class="['lawyer-update-icon', item.type]">
              <template v-if="item.type === 'law'">⚖️</template>
              <template v-else-if="item.type === 'policy'">📋</template>
              <template v-else-if="item.type === 'internal'">🏢</template>
              <template v-else>🔧</template>
            </div>
            <div class="lawyer-update-content">
              <div class="lawyer-flex lawyer-justify-between">
                <h3 class="lawyer-update-title">
                  <nuxt-link :to="`/document/${item.id}`">{{
                    item.title
                  }}</nuxt-link>
                </h3>
                <span class="lawyer-text-light">{{ item.date }}</span>
              </div>
              <p class="lawyer-update-summary">{{ item.description }}</p>

              <!-- AI智能解读 -->
              <div v-if="item.aiSummary?.length" class="lawyer-ai-summary">
                <h4>AI智能解读主要变更点：</h4>
                <ul>
                  <li v-for="point in item.aiSummary" :key="point.title">
                    <strong>{{ point.title }}：</strong>{{ point.content }}
                  </li>
                </ul>
              </div>

              <div
                class="lawyer-flex lawyer-justify-between lawyer-items-center"
              >
                <div class="lawyer-flex lawyer-gap-sm">
                  <span
                    v-for="tag in item.tags"
                    :key="tag"
                    :class="['lawyer-tag', getTagClass(tag)]"
                  >
                    {{ tag }}
                  </span>
                </div>
                <div class="lawyer-flex lawyer-gap-sm">
                  <a
                    @click="viewUpdate(item.id)"
                    class="lawyer-action-btn lawyer-btn-primary"
                    >查看详情</a
                  >
                  <a
                    @click="downloadUpdate(item.id, item.title)"
                    class="lawyer-action-btn"
                    >下载文件</a
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 无内容展示 -->
          <div v-if="!filteredUpdates.length" class="lawyer-no-updates">
            <a-empty description="没有匹配的数据" />
          </div>
        </div>

        <!-- 分页器 -->
        <div class="lawyer-pagination" v-if="filteredUpdates.length">
          <a-pagination
            v-model="currentPage"
            :total="totalCount"
            :page-size="pageSize"
            show-size-changer
            show-quick-jumper
            @change="onPageChange"
            @showSizeChange="onPageSizeChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import { Component, Vue } from "nuxt-property-decorator";
import { RuleUpdateItem } from "~/model/LawyerModel";
import { downloadFileWithMessage } from "~/utils/downloadHelper";

interface AiSummaryPoint {
  title: string;
  content: string;
}

interface UpdateItem {
  id: string;
  title: string;
  description: string;
  date: string;
  source: string;
  category: string;
  type: string;
  tags: string[];
  aiSummary?: AiSummaryPoint[];
}

@Component({
  head: () => ({ title: "法规更新与通知 - 法律合规智能系统" }),
})
export default class UpdatesPage extends Vue {
  activeFilter = ""; // 默认为空，表示全部更新
  currentPage = 1;
  pageSize = 10;
  loading = false;
  updates: UpdateItem[] = [];
  rawUpdates: RuleUpdateItem[] = [];
  allUpdates: UpdateItem[] = []; // 存储所有数据用于前端分页

  filterOptions = [
    { label: "全部更新", value: "" }, // 空字符串表示不传filed参数
    { label: "法律汇编", value: "law" },
    { label: "新规解读", value: "interpretation" },
    { label: "处罚汇编", value: "penalty" },
    { label: "研究集锦", value: "research" },
    { label: "法律合规季刊", value: "quarterly" },
  ];

  async mounted() {
    await this.loadUpdates();
  }

  async loadUpdates() {
    this.loading = true;
    try {
      // 构建查询参数，使用filed参数进行筛选
      const params: any = {};

      // 如果有选择筛选条件，添加filed参数
      if (this.activeFilter) {
        params.filed = this.activeFilter;
      }

      console.log("查询参数:", params);

      // 调用真实API获取数据
      const result = await this.$service.lawyer.getRuleUpdateList(params);
      console.log("获取到的数据:", result);

      if (result && Array.isArray(result)) {
        this.rawUpdates = result;

        // 将真实数据转换为页面显示格式
        this.allUpdates = this.transformRawDataToDisplayFormat(result);

        // 重置到第一页
        this.currentPage = 1;
      } else {
        this.rawUpdates = [];
        this.allUpdates = [];
      }
    } catch (error) {
      console.error("加载更新数据失败", error);
      this.$message.error("加载数据失败，请刷新页面重试");
      this.rawUpdates = [];
      this.allUpdates = [];
    } finally {
      this.loading = false;
    }
  }

  // 将真实API数据转换为页面显示格式
  transformRawDataToDisplayFormat(rawData: RuleUpdateItem[]): UpdateItem[] {
    return rawData.map((item) => {
      // 根据分类确定类型
      const categoryMain = item.categoryMain || "";
      let type = "law";
      if (categoryMain.includes("政策") || categoryMain.includes("监管")) {
        type = "policy";
      } else if (
        categoryMain.includes("内部") ||
        categoryMain.includes("机构")
      ) {
        type = "internal";
      }

      // 生成标签 - 只使用分类标签，过滤空值
      const tags = [item.categoryMain, item.categorySub].filter(Boolean);

      // 生成描述 - 简化逻辑
      const description =
        item.summary ||
        (item.fileContent ? item.fileContent.substring(0, 200) + "..." : "") ||
        "暂无详细描述";

      return {
        id: item.id,
        title: item.ruleName || "未知标题",
        description,
        date: item.createdTimeStr || item.publishDateStr || "未知时间",
        source: item.legalSource || "未知来源",
        category: item.categoryMain || "其他",
        type,
        tags,
        aiSummary: [], // 暂时不生成AI摘要，可以后续根据需要添加
      };
    });
  }

  get filteredUpdates() {
    // API已经根据filed参数进行了筛选，直接返回所有数据
    return this.allUpdates;
  }

  get paginatedUpdates() {
    // 前端分页：计算当前页应该显示的数据
    const start = (this.currentPage - 1) * this.pageSize;
    const end = start + this.pageSize;
    return this.filteredUpdates.slice(start, end);
  }

  get totalCount() {
    // 返回筛选后的总数
    return this.filteredUpdates.length;
  }

  async setActiveFilter(filter: string): Promise<void> {
    this.activeFilter = filter;
    // 重新加载数据（API会根据filed参数筛选）
    await this.loadUpdates();
  }

  viewUpdate(id: string): void {
    // 查找对应的更新项以获取废止状态
    const updateItem = this.rawUpdates.find((item) => item.id === id);
    const isRevoke = !!(
      updateItem?.revokeDateTimestamp || updateItem?.revokeDateStr
    );
    const query = {
      id: id,
      ...(isRevoke ? { isRevoke: "true" } : {}),
    };

    this.$router.push({
      path: "/document",
      query,
    });
  }

  async downloadUpdate(id: string, title: string): Promise<void> {
    try {
      this.$message.loading(`正在准备下载: ${title}`, 0);

      const result = await this.$service.lawyer.downloadRuleFile({
        searchId: id,
      });

      this.$message.destroy();

      downloadFileWithMessage(result, {
        fileName: `${title}.pdf`,
        showMessage: true,
        messageService: this.$message,
      });
    } catch (error) {
      this.$message.destroy();
      console.error("下载失败:", error);
      this.$message.error("下载失败，请检查网络连接后重试");
    }
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    // 前端分页，不需要重新请求API
  }

  onPageSizeChange(current: number, size: number): void {
    this.currentPage = 1;
    this.pageSize = size;
    // 前端分页，不需要重新请求API
  }

  getTagClass(tag: string): string {
    const tagMap: Record<string, string> = {
      重要法规: "lawyer-tag-important",
      资金运用: "lawyer-tag-fund",
      征求意见: "lawyer-tag-opinion",
      偿付能力: "lawyer-tag-solvency",
      风险提示: "lawyer-tag-risk",
      另类投资: "lawyer-tag-alternative",
      机构监管: "lawyer-tag-supervision",
      公司治理: "lawyer-tag-governance",
      行业协会: "lawyer-tag-association",
      风控合规: "lawyer-tag-compliance",
      关联交易: "lawyer-tag-related",
      其他机构: "lawyer-tag-other",
    };
    return tagMap[tag] || "lawyer-tag-default";
  }
}
</script>

<style lang="less">
.updates-page-wrapper {
  .lawyer-updates-header {
    margin-bottom: 24px;
  }

  .lawyer-updates-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
  }

  .lawyer-filter-tabs {
    display: flex;
    gap: 0;
    margin-top: 16px;
  }

  .lawyer-filter-btn {
    padding: 4px 20px;
    background: transparent;
    border: 1px solid var(--lawyer-border);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 13%;

    &:first-child {
      border-radius: 4px 0 0 4px;
    }

    &:last-child {
      border-radius: 0 4px 4px 0;
    }

    &:not(:first-child) {
      margin-left: -1px;
    }

    &:hover {
      color: var(--lawyer-primary);
      border-color: var(--lawyer-primary);
    }

    &.lawyer-filter-btn-active {
      color: var(--lawyer-primary);
      border-color: var(--lawyer-primary);
      background: rgba(var(--lawyer-primary-rgb), 0.1);
      z-index: 1;
    }
  }

  .lawyer-update-item {
    border: 1px solid var(--lawyer-border);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    gap: 16px;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--lawyer-primary);
      background: rgba(var(--lawyer-primary-rgb), 0.02);
    }
  }

  .lawyer-update-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;

    &.law {
      background: rgba(var(--lawyer-danger-rgb), 0.1);
      color: var(--lawyer-danger);
    }

    &.policy {
      background: rgba(var(--lawyer-primary-rgb), 0.1);
      color: var(--lawyer-primary);
    }

    &.internal {
      background: rgba(16, 185, 129, 0.1);
      color: var(--lawyer-success);
    }
  }

  .lawyer-update-content {
    flex: 1;
  }

  .lawyer-update-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    line-height: 1.4;

    a {
      color: var(--lawyer-text);
      text-decoration: none;

      &:hover {
        color: var(--lawyer-primary);
      }
    }
  }

  .lawyer-update-summary {
    color: var(--lawyer-text-light);
    line-height: 1.5;
    margin-bottom: 12px;
  }

  .lawyer-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.2;

    &.lawyer-tag-important,
    &.lawyer-tag-risk {
      background: rgba(239, 68, 68, 0.1);
      color: #ef4444;
    }

    &.lawyer-tag-fund,
    &.lawyer-tag-governance {
      background: rgba(245, 158, 11, 0.1);
      color: var(--lawyer-primary);
    }

    &.lawyer-tag-opinion,
    &.lawyer-tag-association {
      background: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }

    &.lawyer-tag-solvency,
    &.lawyer-tag-compliance {
      background: rgba(114, 46, 209, 0.1);
      color: #722ed1;
    }

    &.lawyer-tag-alternative,
    &.lawyer-tag-related {
      background: rgba(19, 194, 194, 0.1);
      color: #13c2c2;
    }

    &.lawyer-tag-supervision {
      background: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }

    &.lawyer-tag-other,
    &.lawyer-tag-default {
      background: rgba(140, 140, 140, 0.1);
      color: #8c8c8c;
    }
  }

  .lawyer-action-btn {
    padding: 4px 12px;
    border: 1px solid var(--lawyer-border);
    border-radius: 3px;
    background: var(--lawyer-surface);
    color: var(--lawyer-text-light);
    font-size: 12px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--lawyer-primary);
      color: var(--lawyer-primary);
    }

    &.lawyer-btn-primary {
      background: var(--lawyer-primary);
      border-color: var(--lawyer-primary);
      color: white;

      &:hover {
        background: var(--lawyer-primary-dark);
        border-color: var(--lawyer-primary-dark);
      }
    }
  }

  .lawyer-ai-summary {
    margin: 12px 0;
    padding: 12px;
    background: #fffbeb;
    border-radius: 4px;
    border: 1px solid #fef3c7;

    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      color: var(--lawyer-text);
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: var(--lawyer-text-light);
        margin-bottom: 6px;
        line-height: 1.5;
        font-size: 13px;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: var(--lawyer-text);
          font-weight: 500;
        }
      }
    }
  }

  .lawyer-loading-container {
    text-align: center;
    padding: 40px;
  }

  .lawyer-no-updates {
    text-align: center;
    padding: 40px;
  }
}
</style>
